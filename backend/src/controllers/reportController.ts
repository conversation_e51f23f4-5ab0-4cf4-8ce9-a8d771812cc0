import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { IntelligentReportService } from '../services/ai/intelligentReportService';
import { ReportTemplateEngine } from '../services/reports/reportTemplateEngine';
import { AIService } from '../services/aiService';
import { RedisClientWrapper } from '../utils/redisClient';
import {
  ReportGenerationRequest,
  UserRole,
  AppError,
  ValidationError
} from '../types';

/**
 * 智能报表控制器
 * 处理报表生成、管理和导出相关的API请求
 */
export class ReportController {
  private intelligentReportService: IntelligentReportService;
  private reportTemplateEngine: ReportTemplateEngine;

  constructor(
    prisma: PrismaClient,
    aiService: AIService,
    redisClient: RedisClientWrapper
  ) {
    this.intelligentReportService = new IntelligentReportService(prisma, aiService, redisClient);
    this.reportTemplateEngine = new ReportTemplateEngine(prisma);
  }

  /**
   * 生成智能报表
   * POST /api/reports/generate
   */
  async generateReport(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      console.log('📊 收到报表生成请求');

      const {
        templateId,
        userId,
        title,
        config,
        options
      } = req.body as ReportGenerationRequest;

      // 验证必需参数
      if (!userId) {
        throw new ValidationError('用户ID不能为空');
      }

      const request: ReportGenerationRequest = {
        templateId,
        userId,
        title,
        config,
        options
      };

      const result = await this.intelligentReportService.generateRoleBasedReport(request);

      res.json({
        success: true,
        data: result,
        message: '报表生成请求已提交，正在处理中'
      });

      console.log(`✅ 报表生成请求处理成功: ${result.reportId}`);
    } catch (error) {
      console.error('❌ 报表生成请求处理失败:', error);
      next(error);
    }
  }

  /**
   * 获取报表详情
   * GET /api/reports/:reportId
   */
  async getReport(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { reportId } = req.params;

      if (!reportId) {
        throw new ValidationError('报表ID不能为空');
      }

      const report = await this.intelligentReportService.getReportInstance(reportId);

      if (!report) {
        throw new AppError('报表不存在', 404);
      }

      res.json({
        success: true,
        data: report,
        message: '获取报表详情成功'
      });

      console.log(`✅ 获取报表详情成功: ${reportId}`);
    } catch (error) {
      console.error('❌ 获取报表详情失败:', error);
      next(error);
    }
  }

  /**
   * 获取用户报表列表
   * GET /api/reports/user/:userId
   */
  async getUserReports(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userId } = req.params;
      const { limit = '10' } = req.query;

      if (!userId) {
        throw new ValidationError('用户ID不能为空');
      }

      const reports = await this.intelligentReportService.getUserReports(
        userId,
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: reports,
        message: `获取用户报表列表成功，共${reports.length}个报表`
      });

      console.log(`✅ 获取用户报表列表成功: ${userId}, 数量: ${reports.length}`);
    } catch (error) {
      console.error('❌ 获取用户报表列表失败:', error);
      next(error);
    }
  }

  /**
   * 获取报表预览
   * GET /api/reports/:reportId/preview
   */
  async getReportPreview(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { reportId } = req.params;

      if (!reportId) {
        throw new ValidationError('报表ID不能为空');
      }

      const report = await this.intelligentReportService.getReportInstance(reportId);

      if (!report) {
        throw new AppError('报表不存在', 404);
      }

      // 返回简化的预览数据
      const preview = {
        id: report.id,
        title: report.title,
        status: report.status,
        generatedAt: report.generatedAt,
        executiveSummary: report.content.executiveSummary,
        sectionCount: report.content.sections.length,
        insightCount: report.content.keyInsights.length,
        confidence: report.content.metadata.confidence
      };

      res.json({
        success: true,
        data: preview,
        message: '获取报表预览成功'
      });

      console.log(`✅ 获取报表预览成功: ${reportId}`);
    } catch (error) {
      console.error('❌ 获取报表预览失败:', error);
      next(error);
    }
  }

  /**
   * 创建报表模板
   * POST /api/reports/templates
   */
  async createTemplate(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      console.log('🎨 收到创建报表模板请求');

      const {
        name,
        description,
        config,
        userRole,
        domain,
        isPublic = false,
        createdBy
      } = req.body;

      // 验证必需参数
      if (!name || !config || !userRole || !domain || !createdBy) {
        throw new ValidationError('缺少必需参数：name, config, userRole, domain, createdBy');
      }

      const template = await this.reportTemplateEngine.createTemplate({
        name,
        description,
        config,
        userRole: userRole as UserRole,
        domain,
        isPublic,
        createdBy
      });

      res.status(201).json({
        success: true,
        data: template,
        message: '报表模板创建成功'
      });

      console.log(`✅ 报表模板创建成功: ${template.id}`);
    } catch (error) {
      console.error('❌ 报表模板创建失败:', error);
      next(error);
    }
  }

  /**
   * 获取可用模板列表
   * GET /api/reports/templates
   */
  async getAvailableTemplates(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userRole, userId } = req.query;

      if (!userRole) {
        throw new ValidationError('用户角色不能为空');
      }

      const templates = await this.reportTemplateEngine.getAvailableTemplates(
        userRole as UserRole,
        userId as string
      );

      res.json({
        success: true,
        data: templates,
        message: `获取可用模板列表成功，共${templates.length}个模板`
      });

      console.log(`✅ 获取可用模板列表成功: ${userRole}, 数量: ${templates.length}`);
    } catch (error) {
      console.error('❌ 获取可用模板列表失败:', error);
      next(error);
    }
  }

  /**
   * 获取模板详情
   * GET /api/reports/templates/:templateId
   */
  async getTemplate(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { templateId } = req.params;

      if (!templateId) {
        throw new ValidationError('模板ID不能为空');
      }

      const template = await this.reportTemplateEngine.getTemplate(templateId);

      if (!template) {
        throw new AppError('模板不存在', 404);
      }

      res.json({
        success: true,
        data: template,
        message: '获取模板详情成功'
      });

      console.log(`✅ 获取模板详情成功: ${templateId}`);
    } catch (error) {
      console.error('❌ 获取模板详情失败:', error);
      next(error);
    }
  }

  /**
   * 更新报表模板
   * PUT /api/reports/templates/:templateId
   */
  async updateTemplate(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { templateId } = req.params;
      const updateData = req.body;

      if (!templateId) {
        throw new ValidationError('模板ID不能为空');
      }

      const template = await this.reportTemplateEngine.updateTemplate(templateId, updateData);

      res.json({
        success: true,
        data: template,
        message: '报表模板更新成功'
      });

      console.log(`✅ 报表模板更新成功: ${templateId}`);
    } catch (error) {
      console.error('❌ 报表模板更新失败:', error);
      next(error);
    }
  }

  /**
   * 删除报表模板
   * DELETE /api/reports/templates/:templateId
   */
  async deleteTemplate(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { templateId } = req.params;

      if (!templateId) {
        throw new ValidationError('模板ID不能为空');
      }

      await this.reportTemplateEngine.deleteTemplate(templateId);

      res.json({
        success: true,
        message: '报表模板删除成功'
      });

      console.log(`✅ 报表模板删除成功: ${templateId}`);
    } catch (error) {
      console.error('❌ 报表模板删除失败:', error);
      next(error);
    }
  }

  /**
   * 复制报表模板
   * POST /api/reports/templates/:templateId/clone
   */
  async cloneTemplate(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { templateId } = req.params;
      const { newName, createdBy } = req.body;

      if (!templateId || !newName || !createdBy) {
        throw new ValidationError('缺少必需参数：newName, createdBy');
      }

      const clonedTemplate = await this.reportTemplateEngine.cloneTemplate(
        templateId,
        newName,
        createdBy
      );

      res.status(201).json({
        success: true,
        data: clonedTemplate,
        message: '报表模板复制成功'
      });

      console.log(`✅ 报表模板复制成功: ${templateId} -> ${clonedTemplate.id}`);
    } catch (error) {
      console.error('❌ 报表模板复制失败:', error);
      next(error);
    }
  }

  /**
   * 获取预定义模板
   * GET /api/reports/templates/predefined
   */
  async getPreDefinedTemplates(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const templates = await this.reportTemplateEngine.getPreDefinedTemplates();

      res.json({
        success: true,
        data: templates,
        message: `获取预定义模板成功，共${templates.length}个模板`
      });

      console.log(`✅ 获取预定义模板成功，数量: ${templates.length}`);
    } catch (error) {
      console.error('❌ 获取预定义模板失败:', error);
      next(error);
    }
  }

  /**
   * 初始化默认模板
   * POST /api/reports/templates/initialize
   */
  async initializeDefaultTemplates(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      console.log('🔧 收到初始化默认模板请求');

      await this.reportTemplateEngine.initializeDefaultTemplates();

      res.json({
        success: true,
        message: '默认模板初始化成功'
      });

      console.log('✅ 默认模板初始化成功');
    } catch (error) {
      console.error('❌ 默认模板初始化失败:', error);
      next(error);
    }
  }
}
