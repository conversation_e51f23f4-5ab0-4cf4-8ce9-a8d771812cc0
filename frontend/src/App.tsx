import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box } from '@mui/material';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import DataSources from './pages/DataSources';
import SemanticModel from './pages/SemanticModel';
import RelationshipReview from './pages/RelationshipReview';
import Chat from './pages/Chat';

function App() {
  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/data-sources" element={<DataSources />} />
          <Route path="/semantic-model" element={<SemanticModel />} />
          <Route path="/relationship-review" element={<RelationshipReview />} />
          <Route path="/chat" element={<Chat />} />
        </Routes>
      </Layout>
    </Box>
  );
}

export default App;
