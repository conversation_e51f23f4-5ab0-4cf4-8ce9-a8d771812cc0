import axios, { AxiosResponse } from 'axios';
import {
  ApiResponse,
  DataSourceListItem,
  DataSourceSchema,
  CreateDataSourceRequest,
  ChatRequest,
  ChatResponse,
  UpdateTableAliasRequest,
  UpdateColumnAliasRequest,
  CreateRelationshipRequest,
  ConnectionConfig,
  DataSourceType
} from '../types';

// 创建 axios 实例
const api = axios.create({
  baseURL: import.meta.env.REACT_APP_API_URL || 'http://localhost:3001',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response;
  },
  (error) => {
    // 统一错误处理
    const message = error.response?.data?.error || error.message || '请求失败';
    return Promise.reject(new Error(message));
  }
);

// 数据源相关API
export const dataSourceApi = {
  // 获取所有数据源
  getDataSources: async (): Promise<DataSourceListItem[]> => {
    const response = await api.get<ApiResponse<DataSourceListItem[]>>('/api/data-sources');
    return response.data.data || [];
  },

  // 根据ID获取数据源详情
  getDataSourceById: async (id: string): Promise<DataSourceSchema> => {
    const response = await api.get<ApiResponse<DataSourceSchema>>(`/api/data-sources/${id}`);
    return response.data.data!;
  },

  // 获取数据源Schema
  getDataSourceSchema: async (id: string): Promise<DataSourceSchema> => {
    const response = await api.get<ApiResponse<DataSourceSchema>>(`/api/data-sources/${id}/schema`);
    return response.data.data!;
  },

  // 创建数据源
  createDataSource: async (data: CreateDataSourceRequest): Promise<string> => {
    const response = await api.post<ApiResponse<{ id: string }>>('/api/data-sources', data);
    return response.data.data!.id;
  },

  // 更新数据源
  updateDataSource: async (id: string, data: Partial<CreateDataSourceRequest>): Promise<void> => {
    await api.put(`/api/data-sources/${id}`, data);
  },

  // 删除数据源
  deleteDataSource: async (id: string): Promise<void> => {
    await api.delete(`/api/data-sources/${id}`);
  },

  // 测试连接
  testConnection: async (type: DataSourceType, connectionConfig: ConnectionConfig): Promise<boolean> => {
    const response = await api.post<ApiResponse<{ connected: boolean }>>('/api/data-sources/test-connection', {
      type,
      connectionConfig
    });
    return response.data.data!.connected;
  },

  // 同步元数据
  syncMetadata: async (id: string): Promise<void> => {
    await api.post(`/api/data-sources/${id}/sync`);
  },

  // 检查连接状态
  checkConnectionStatus: async (id: string): Promise<string> => {
    const response = await api.get<ApiResponse<{ status: string }>>(`/api/data-sources/${id}/status`);
    return response.data.data!.status;
  }
};

// 语义层相关API
export const semanticApi = {
  // 获取语义层上下文
  getSemanticContext: async (): Promise<string> => {
    const response = await api.get<ApiResponse<{ context: string }>>('/api/semantic/context');
    return response.data.data!.context;
  },

  // 更新表别名
  updateTableAlias: async (tableId: string, data: UpdateTableAliasRequest): Promise<void> => {
    await api.put(`/api/semantic/tables/${tableId}/alias`, data);
  },

  // 更新列别名
  updateColumnAlias: async (columnId: string, data: UpdateColumnAliasRequest): Promise<void> => {
    await api.put(`/api/semantic/columns/${columnId}/alias`, data);
  },

  // 创建关联关系
  createRelationship: async (data: CreateRelationshipRequest): Promise<string> => {
    const response = await api.post<ApiResponse<{ id: string }>>('/api/semantic/relationships', data);
    return response.data.data!.id;
  },

  // 更新关联关系
  updateRelationship: async (relationshipId: string, relationshipType: string): Promise<void> => {
    await api.put(`/api/semantic/relationships/${relationshipId}`, { relationshipType });
  },

  // 删除关联关系
  deleteRelationship: async (relationshipId: string): Promise<void> => {
    await api.delete(`/api/semantic/relationships/${relationshipId}`);
  },

  // 自动发现关联关系
  discoverRelationships: async (dataSourceId: string): Promise<number> => {
    const response = await api.post<ApiResponse<{ discoveredCount: number }>>(
      `/api/semantic/data-sources/${dataSourceId}/discover-relationships`
    );
    return response.data.data!.discoveredCount;
  },

  // 发现跨数据源关联关系
  discoverCrossSourceRelationships: async (
    sourceDataSourceId: string,
    targetDataSourceId?: string,
    options?: {
      confidenceThreshold?: number;
      maxSuggestions?: number;
      autoApply?: boolean;
      autoApplyThreshold?: number;
    }
  ): Promise<{
    discoveredCount: number;
    appliedCount: number;
    relationships: any[];
    applyResults?: any;
  }> => {
    const response = await api.post<ApiResponse<{
      discoveredCount: number;
      appliedCount: number;
      relationships: any[];
      applyResults?: any;
    }>>(
      `/api/semantic/data-sources/${sourceDataSourceId}/discover-cross-relationships`,
      {
        targetDataSourceId,
        confidenceThreshold: options?.confidenceThreshold || 0.3,
        maxSuggestions: options?.maxSuggestions || 50,
        autoApply: options?.autoApply || false,
        autoApplyThreshold: options?.autoApplyThreshold || 0.8
      }
    );
    return response.data.data!;
  },

  // 批量发现所有关联关系
  discoverAllRelationships: async (options?: {
    enableForeignKeyDetection?: boolean;
    enableNamingConvention?: boolean;
    enableDataAnalysis?: boolean;
    enableCrossDataSource?: boolean;
    confidenceThreshold?: number;
    maxSuggestions?: number;
    autoApply?: boolean;
    autoApplyThreshold?: number;
  }): Promise<{
    discoveredCount: number;
    appliedCount: number;
    relationships: any[];
    statistics?: any;
    applyResults?: any;
  }> => {
    const response = await api.post<ApiResponse<{
      discoveredCount: number;
      appliedCount: number;
      relationships: any[];
      statistics?: any;
      applyResults?: any;
    }>>(
      '/api/semantic/discover-all-relationships',
      {
        enableForeignKeyDetection: options?.enableForeignKeyDetection ?? true,
        enableNamingConvention: options?.enableNamingConvention ?? true,
        enableDataAnalysis: options?.enableDataAnalysis ?? true,
        enableCrossDataSource: options?.enableCrossDataSource ?? true,
        confidenceThreshold: options?.confidenceThreshold || 0.3,
        maxSuggestions: options?.maxSuggestions || 100,
        autoApply: options?.autoApply || false,
        autoApplyThreshold: options?.autoApplyThreshold || 0.8
      }
    );
    return response.data.data!;
  },

  // 应用发现的关联关系
  applyDiscoveredRelationships: async (
    relationships: any[],
    autoApplyThreshold?: number
  ): Promise<{
    applied: number;
    skipped: number;
    failed: number;
    results: any[];
  }> => {
    const response = await api.post<ApiResponse<{
      applied: number;
      skipped: number;
      failed: number;
      results: any[];
    }>>(
      '/api/semantic/apply-relationships',
      {
        relationships,
        autoApplyThreshold: autoApplyThreshold || 0.8
      }
    );
    return response.data.data!;
  }
};

// 聊天相关API
export const chatApi = {
  // 发送聊天消息
  sendMessage: async (data: ChatRequest): Promise<ChatResponse> => {
    const response = await api.post<ApiResponse<ChatResponse>>('/api/chat', data);
    return response.data.data!;
  },

  // 获取聊天历史
  getChatHistory: async (sessionId: string, limit?: number): Promise<ChatResponse[]> => {
    const params = limit ? { limit } : {};
    const response = await api.get<ApiResponse<ChatResponse[]>>(`/api/chat/history/${sessionId}`, { params });
    return response.data.data || [];
  }
};

export default api;
