// 数据源类型
export type DataSourceType = 'postgresql' | 'mysql' | 'csv' | 'excel';

// 数据源状态
export type DataSourceStatus = 'connected' | 'disconnected' | 'error' | 'syncing';

// 关联关系类型
export type RelationshipType = 'one_to_one' | 'one_to_many' | 'many_to_one';

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 数据源列表项
export interface DataSourceListItem {
  id: string;
  name: string;
  type: DataSourceType;
  status: DataSourceStatus;
  createdAt: string;
  updatedAt: string;
}

// 数据表信息
export interface DataTableInfo {
  id: string;
  originalName: string;
  aliasName?: string;
  description?: string;
  columns: DataColumnInfo[];
}

// 数据列信息
export interface DataColumnInfo {
  id: string;
  originalName: string;
  aliasName?: string;
  dataType: string;
  description?: string;
  isPrimaryKey: boolean;
}

// 关联关系信息
export interface DataRelationshipInfo {
  id: string;
  fromColumnId: string;
  toColumnId: string;
  type: RelationshipType;
  isManual: boolean;
}

// 数据源Schema详情
export interface DataSourceSchema {
  id: string;
  name: string;
  type: DataSourceType;
  tables: DataTableInfo[];
  relationships: DataRelationshipInfo[];
}

// 数据库连接配置
export interface DatabaseConnectionConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}

// 文件连接配置
export interface FileConnectionConfig {
  filename: string;
  path: string;
  encoding?: string;
  delimiter?: string; // for CSV
  sheetName?: string; // for Excel
}

export type ConnectionConfig = DatabaseConnectionConfig | FileConnectionConfig;

// 数据源创建请求
export interface CreateDataSourceRequest {
  name: string;
  type: DataSourceType;
  connectionConfig: ConnectionConfig;
}

// 聊天请求
export interface ChatRequest {
  sessionId: string;
  prompt: string;
}

// 聊天响应
export interface ChatResponse {
  id: string;
  sessionId: string;
  userPrompt: string;
  responseText: string;
  data?: {
    columns: string[];
    rows: any[][];
  };
  visualization?: {
    type: string;
    title: string;
    spec: any;
  };
  generatedSql?: string;
}

// 可视化图表类型
export type VisualizationType = 'bar_chart' | 'line_chart' | 'pie_chart' | 'table' | 'scatter_plot';

// 表单状态
export interface FormState<T> {
  data: T;
  errors: Partial<Record<keyof T, string>>;
  isSubmitting: boolean;
}

// 模态框状态
export interface ModalState {
  open: boolean;
  mode: 'create' | 'edit' | 'view';
  data?: any;
}

// 语义层更新请求
export interface UpdateTableAliasRequest {
  aliasName?: string;
  description?: string;
}

export interface UpdateColumnAliasRequest {
  aliasName?: string;
  description?: string;
}

export interface CreateRelationshipRequest {
  fromColumnId: string;
  toColumnId: string;
  relationshipType: RelationshipType;
}

// 关联关系发现结果
export interface RelationshipDiscoveryResult {
  id: string;
  fromTableName: string;
  fromColumnName: string;
  toTableName: string;
  toColumnName: string;
  relationshipType: RelationshipType;
  confidence: number;
  reasons: string[];
  isApplied: boolean;
  dataSourceId?: string;
  targetDataSourceId?: string;
}

// 关联关系发现配置
export interface RelationshipDiscoveryConfig {
  enableForeignKeyDetection: boolean;
  enableNamingConvention: boolean;
  enableDataAnalysis: boolean;
  enableCrossDataSource: boolean;
  confidenceThreshold: number;
  maxSuggestions: number;
  autoApply: boolean;
  autoApplyThreshold: number;
}

// 关联关系发现响应
export interface RelationshipDiscoveryResponse {
  discoveredCount: number;
  appliedCount: number;
  relationships: RelationshipDiscoveryResult[];
  statistics?: {
    highConfidence: number;
    mediumConfidence: number;
    lowConfidence: number;
    averageConfidence: number;
  };
  applyResults?: {
    applied: number;
    skipped: number;
    failed: number;
    results: any[];
  };
}

// 关联关系应用结果
export interface RelationshipApplyResult {
  applied: number;
  skipped: number;
  failed: number;
  results: Array<{
    relationship: RelationshipDiscoveryResult;
    status: 'applied' | 'skipped' | 'failed';
    error?: string;
  }>;
}
